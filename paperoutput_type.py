import logging
import re
from pathlib import Path

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# 0. ─── 日志设置 ──────────────────────────────────────────
logging.basicConfig(
    level=logging.INFO,         
    format='[%(levelname)s] %(message)s'
)

# 1. ─── 路径与列索引 ───────────────────────────────────────
xls_path = Path(
    r"C:\AI医疗\intensive care unit; nursing2025.8.6 原始数据及任务要求\2025.8.6导出数据.xlsx"
)
year_col_idx = 27    # AB列（日期/年份）
type_col_idx = 22    # W列（文献种类）

# 2. ─── 读取数据 ──────────────────────────────────────────
# 修正：AB列=文献类型, W列=日期
raw = pd.read_excel(xls_path, usecols=[year_col_idx, type_col_idx])

# 重要修正：AB列实际是文献类型，W列实际是日期
raw.columns = ["literature_type", "raw_date"]  # AB列=文献类型, W列=日期

expected_rows = 4268
actual_rows = len(raw)

logging.info(f"读取数据完成，共 {actual_rows} 行；目标应有 {expected_rows} 行")
logging.info(f"列映射修正: AB列=文献类型, W列=日期")
if actual_rows != expected_rows:
    logging.warning("⚠️ 行数与期望不符，请确认 Excel 是否改动！")

# 检查原始数据样本
logging.info("原始数据样本（前10行）:")
logging.info("=" * 50)
for i, (type_val, date_val) in enumerate(zip(raw["literature_type"].head(10), raw["raw_date"].head(10))):
    logging.info(f"行 {i+1}: AB列(文献类型)='{type_val}', W列(日期)='{date_val}'")

logging.info("\n原始数据统计:")
logging.info(f"AB列(文献类型)非空值数量: {raw['literature_type'].notna().sum()}")
logging.info(f"W列(日期)非空值数量: {raw['raw_date'].notna().sum()}")
logging.info(f"AB列(文献类型)唯一值示例: {raw['literature_type'].dropna().unique()[:5]}")
logging.info(f"W列(日期)唯一值示例: {raw['raw_date'].dropna().unique()[:5]}")

# 3. ─── 年份抽取函数 ───────────────────────────────────────
def extract_year(text: str | float) -> int | None:
    """
    先用正则抓 4 位数字；抓不到再走 to_datetime；
    仍解析失败则返回 None
    """
    if pd.isna(text):
        return None

    # step 1: 正则
    m = re.search(r"\b(\d{4})\b", str(text))
    if m:
        yr = int(m.group(1))
        if 1800 <= yr <= 2100:     # 简单年份范围校验
            return yr

    # step 2: datetime 兜底
    try:
        return pd.to_datetime(text, errors="raise").year
    except Exception:
        return None

# 4. ─── 文献类型分割函数 ───────────────────────────────────
def split_literature_types(type_string):
    """
    将文献类型字符串按照 || 分割成多个独立类型
    返回清理后的类型列表
    """
    if pd.isna(type_string) or type_string == '' or str(type_string).lower() in ['nan', 'none']:
        return ['未知类型']
    
    # 按照 || 分割
    types = str(type_string).split('||')
    
    # 清理每个类型：去除首尾空格，处理空值
    cleaned_types = []
    for t in types:
        cleaned = t.strip()
        if cleaned and cleaned.lower() not in ['nan', 'none', '']:
            cleaned_types.append(cleaned)
    
    # 如果清理后没有有效类型，返回未知类型
    return cleaned_types if cleaned_types else ['未知类型']

# 5. ─── 数据预处理 ────────────────────────────────────────
# 提取年份（现在从正确的日期列）
logging.info("开始年份提取...")
raw["Year"] = raw["raw_date"].apply(extract_year)

# 检查年份提取结果
year_extract_success = raw["Year"].notna().sum()
year_extract_failed = raw["Year"].isna().sum()
logging.info(f"年份提取结果: 成功 {year_extract_success} 行, 失败 {year_extract_failed} 行")

if year_extract_failed > 0:
    logging.warning(f"年份提取失败的前5个样本:")
    failed_samples = raw[raw["Year"].isna()]["raw_date"].head(5)
    for i, sample in enumerate(failed_samples):
        logging.warning(f"  失败样本 {i+1}: '{sample}' (类型: {type(sample)})")

# 移除年份解析失败的行
clean_data = raw.dropna(subset=['Year']).copy()
clean_data["Year"] = clean_data["Year"].astype(int)

logging.info(f"年份提取后数据行数: {len(clean_data)}")

# 如果清理后数据为空，尝试更宽松的年份提取
if len(clean_data) == 0:
    logging.warning("⚠️ 标准年份提取失败，尝试更宽松的提取方法...")
    
    def loose_extract_year(text):
        """更宽松的年份提取方法，专门处理如'2020 Nov 13'这样的日期格式"""
        if pd.isna(text):
            return None
        
        text_str = str(text)
        logging.debug(f"尝试解析: '{text_str}'")
        
        # 尝试多种年份模式
        patterns = [
            r'(\d{4})',  # 任意4位数字（优先匹配年份）
            r'(\d{2,4})',  # 2-4位数字
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text_str)
            for match in matches:
                try:
                    year = int(match)
                    # 扩大年份范围检查
                    if 1900 <= year <= 2100:
                        return year
                    # 如果是2位数，假设是近期年份
                    elif 50 <= year <= 99:
                        return 1900 + year
                    elif 0 <= year <= 49:
                        return 2000 + year
                except ValueError:
                    continue
        
        return None
    
    # 重新尝试年份提取
    raw["Year"] = raw["raw_date"].apply(loose_extract_year)
    clean_data = raw.dropna(subset=['Year']).copy()
    clean_data["Year"] = clean_data["Year"].astype(int)
    
    logging.info(f"宽松提取后数据行数: {len(clean_data)}")
    
    if len(clean_data) > 0:
        year_range = clean_data["Year"].agg(['min', 'max'])
        logging.info(f"提取的年份范围: {year_range['min']} - {year_range['max']}")
    else:
        # 最后尝试：显示所有原始日期数据的类型和内容
        logging.error("所有年份提取方法都失败了，显示原始日期数据详情:")
        for i in range(min(10, len(raw))):
            val = raw.iloc[i]["raw_date"]
            logging.error(f"行 {i+1}: 日期值='{val}', 类型={type(val)}, 字符串='{str(val)}'")
        
        raise ValueError("无法从任何行中提取有效年份")

# 6. ─── 文献类型分割处理 ────────────────────────────────────
logging.info("开始文献类型分割处理...")

# 展示几个分割示例
logging.info("文献类型分割示例:")
sample_types = clean_data["literature_type"].dropna().head(5)
for i, original_type in enumerate(sample_types):
    split_types = split_literature_types(original_type)
    logging.info(f"  示例 {i+1}: '{original_type}' -> {split_types}")

# 创建展开后的数据框
expanded_data = []
for idx, row in clean_data.iterrows():
    year = row["Year"]
    original_types = row["literature_type"]
    
    # 分割文献类型
    split_types = split_literature_types(original_types)
    
    # 为每个分割后的类型创建一行记录
    for lit_type in split_types:
        expanded_data.append({
            'Year': year,
            'literature_type': lit_type,
            'original_row': idx
        })

# 转换为DataFrame
expanded_df = pd.DataFrame(expanded_data)
logging.info(f"原始数据行数: {len(clean_data)}")
logging.info(f"展开后数据行数: {len(expanded_df)} (因为一篇文献可能有多个类型)")

# 7. ─── 数据有效性检查 ────────────────────────────────────
logging.info(f"展开后数据统计:")
logging.info(f"  - 总记录数: {len(expanded_df)}")
logging.info(f"  - 年份非空记录数: {expanded_df['Year'].notna().sum()}")
logging.info(f"  - 文献类型非空记录数: {expanded_df['literature_type'].notna().sum()}")

# 进一步清理：移除年份或文献类型为空的记录
expanded_df = expanded_df.dropna(subset=['Year', 'literature_type']).copy()
logging.info(f"最终清理后记录数: {len(expanded_df)}")

if len(expanded_df) == 0:
    logging.error("❌ 清理后没有有效数据！请检查Excel文件的AB列和W列")
    raise ValueError("没有有效的数据进行分析")

# 检查年份数据类型和范围
year_stats = expanded_df['Year'].describe()
logging.info(f"年份统计信息:\n{year_stats}")

# 检查文献类型分布
type_counts = expanded_df["literature_type"].value_counts()
logging.info("文献类型分布（分割后）：")
for lit_type, count in type_counts.head(15).items():  # 显示前15个
    logging.info(f"  {lit_type}: {count}")
if len(type_counts) > 15:
    logging.info(f"  ... 还有 {len(type_counts) - 15} 种其他类型")

# 8. ─── 创建年份-文献类型交叉表 ─────────────────────────────
try:
    crosstab = pd.crosstab(expanded_df["Year"], expanded_df["literature_type"])
    logging.info(f"交叉表创建成功，形状: {crosstab.shape}")
    
    # 检查交叉表是否为空
    if crosstab.empty:
        logging.error("❌ 交叉表为空！")
        raise ValueError("交叉表为空，无法绘图")
    
    # 检查年份范围
    min_year, max_year = crosstab.index.min(), crosstab.index.max()
    logging.info(f"年份范围: {min_year} 到 {max_year}")
    
    # 验证年份范围的有效性
    if pd.isna(min_year) or pd.isna(max_year):
        logging.error("❌ 年份范围包含NaN值！")
        raise ValueError("年份数据包含无效值")
        
except Exception as e:
    logging.error(f"❌ 创建交叉表时出错: {e}")
    logging.info("尝试查看原始数据样本:")
    logging.info(expanded_df.head(10).to_string())
    raise

# 9. ─── 使用全部文献类型 ─────────────────────────────────
# 显示所有文献类型，不进行合并
type_totals = crosstab.sum().sort_values(ascending=False)

# 使用完整的交叉表
simplified_crosstab = crosstab.copy()

logging.info(f"显示图表中包含的所有类型数量: {len(simplified_crosstab.columns)}")
logging.info(f"前20个最常见的类型: {list(type_totals.head(20).index)}")

# 10. ─── 绘制堆积图 ───────────────────────────────────────
sns.set_theme(style="whitegrid")
plt.rcParams["font.family"] = ["SimHei"]
plt.rcParams["axes.unicode_minus"] = False

# 获取年份和文献类型信息
years = simplified_crosstab.index.values
n_types = len(simplified_crosstab.columns)

# 验证数据完整性
if len(years) == 0:
    logging.error("❌ 没有有效的年份数据！")
    raise ValueError("年份数据为空")

logging.info(f"准备绘图 - 年份数: {len(years)}, 所有文献类型数: {n_types}")

# 设置颜色调色板 - 如果类型很多，使用更大的调色板
if n_types <= 20:
    colors = plt.cm.Set3(np.linspace(0, 1, n_types))
else:
    # 对于更多类型，组合多个调色板
    colors1 = plt.cm.Set3(np.linspace(0, 1, 12))
    colors2 = plt.cm.Set2(np.linspace(0, 1, 8))
    colors3 = plt.cm.Pastel1(np.linspace(0, 1, 9))
    colors4 = plt.cm.Pastel2(np.linspace(0, 1, 8))
    colors5 = plt.cm.Dark2(np.linspace(0, 1, 8))
    all_colors = np.vstack([colors1, colors2, colors3, colors4, colors5])
    colors = all_colors[:n_types]

fig, ax = plt.subplots(figsize=(16, 10), dpi=300)

# 绘制堆积面积图并收集标签位置信息
bottom = np.zeros(len(years))
label_positions = []  # 存储标签位置信息

for i, (lit_type, color) in enumerate(zip(simplified_crosstab.columns, colors)):
    values = simplified_crosstab[lit_type].values
    
    # 检查数值有效性
    if np.any(np.isnan(values)) or np.any(np.isinf(values)):
        logging.warning(f"⚠️ 文献类型 '{lit_type}' 包含无效值，跳过")
        continue
    
    ax.fill_between(years, bottom, bottom + values, 
                   label=lit_type, color=color, alpha=0.8)
    
    # 为每个区域添加边界线
    ax.plot(years, bottom + values, color='white', linewidth=0.5, alpha=0.7)
    
    # 计算标签位置 - 找到每个类型的最大区域
    max_height = np.max(values)
    max_idx = np.argmax(values)
    
    if max_height > 0:  # 只有当高度大于0时才考虑添加标签
        y_center = bottom[max_idx] + values[max_idx] / 2
        x_center = years[max_idx]
        
        # 存储标签信息
        label_positions.append({
            'text': lit_type,
            'x': x_center,
            'y': y_center,
            'height': values[max_idx],
            'color': color
        })
    
    bottom += values

# 智能添加标签
def get_text_color(bg_color):
    """根据背景色选择最佳文字颜色"""
    # 计算背景色的亮度
    r, g, b = bg_color[:3]
    brightness = (r * 299 + g * 587 + b * 114) / 1000
    return 'white' if brightness < 0.5 else 'black'

def truncate_text(text, max_length=20):
    """截断过长的文本，不添加省略号"""
    if len(text) <= max_length:
        return text
    return text[:max_length]

# 获取图表的总高度用于计算相对大小
total_height = np.max(bottom)
min_height_threshold = total_height * 0.02  # 最小高度阈值（相对于总高度的2%）

# 按照区域大小排序，优先显示大区域的标签
label_positions.sort(key=lambda x: x['height'], reverse=True)

# 添加文本标签
added_labels = []
for label_info in label_positions:
    if label_info['height'] >= min_height_threshold:  # 只在足够大的区域添加标签
        # 选择合适的字体大小
        if label_info['height'] >= total_height * 0.1:
            fontsize = 9
            fontweight = 'bold'
        elif label_info['height'] >= total_height * 0.05:
            fontsize = 8
            fontweight = 'normal'
        else:
            fontsize = 7
            fontweight = 'normal'
        
        # 截断过长的文本
        display_text = truncate_text(label_info['text'])
        
        # 选择文字颜色
        text_color = get_text_color(label_info['color'])
        
        # 检查标签位置是否会重叠（简单的重叠检测）
        overlap = False
        for existing in added_labels:
            if (abs(existing['x'] - label_info['x']) < (years.max() - years.min()) * 0.1 and 
                abs(existing['y'] - label_info['y']) < total_height * 0.05):
                overlap = True
                break
        
        if not overlap:
            ax.text(label_info['x'], label_info['y'], display_text,
                   ha='center', va='center',
                   fontsize=fontsize, fontweight=fontweight,
                   color=text_color,
                   bbox=dict(boxstyle="round,pad=0.2", 
                           facecolor='white', 
                           edgecolor='none', 
                           alpha=0.7),
                   zorder=10)
            
            added_labels.append({'x': label_info['x'], 'y': label_info['y']})

logging.info(f"已添加 {len(added_labels)} 个区域标签")

# 设置图表样式
ax.set_xlabel("年份", fontsize=12, fontweight='bold')
ax.set_ylabel("论文数量", fontsize=12, fontweight='bold')
ax.set_title("图2  各年份文献种类统计堆积图", fontsize=18, fontweight='bold', pad=25)

# 安全设置x轴范围
try:
    min_year, max_year = int(years.min()), int(years.max())
    ax.set_xlim(min_year - 0.5, max_year + 0.5)
    
    # 设置x轴刻度
    year_range = max_year - min_year
    if year_range <= 5:
        x_ticks = range(min_year, max_year + 1)
    elif year_range <= 20:
        x_ticks = range(min_year, max_year + 1, 2)
    else:
        step = max(1, year_range // 10)
        x_ticks = range(min_year, max_year + 1, step)
    
    ax.set_xticks(x_ticks)
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
    
    logging.info(f"x轴设置完成: {min_year} - {max_year}")
    
except Exception as e:
    logging.error(f"❌ 设置x轴时出错: {e}")
    # 使用默认设置
    ax.set_xticks(years[::max(1, len(years)//10)])
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right")

# 设置图例 - 对于多类型情况优化布局，使其更适合论文
legend_fontsize = max(6, min(10, 120 // n_types))  # 根据类型数量动态调整字体大小

if n_types <= 12:
    legend = ax.legend(bbox_to_anchor=(1.02, 1), loc='upper left', 
                      frameon=True, fancybox=True, shadow=True,
                      fontsize=legend_fontsize)
elif n_types <= 24:
    legend = ax.legend(bbox_to_anchor=(1.02, 1), loc='upper left', 
                      frameon=True, fancybox=True, shadow=True, 
                      ncol=2, fontsize=legend_fontsize)
elif n_types <= 36:
    legend = ax.legend(bbox_to_anchor=(1.02, 1), loc='upper left', 
                      frameon=True, fancybox=True, shadow=True, 
                      ncol=3, fontsize=legend_fontsize)
else:
    legend = ax.legend(bbox_to_anchor=(1.02, 1), loc='upper left', 
                      frameon=True, fancybox=True, shadow=True, 
                      ncol=4, fontsize=legend_fontsize)

# 设置图例样式，使其更加优雅
legend.get_frame().set_facecolor('white')
legend.get_frame().set_alpha(0.95)
legend.get_frame().set_edgecolor('lightgray')
legend.get_frame().set_linewidth(0.5)

# 添加网格和美化
ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
sns.despine(ax=ax, top=True, right=True)

# 设置坐标轴样式
ax.tick_params(axis='both', which='major', labelsize=10)
ax.set_xlabel("年份", fontsize=14, fontweight='bold', labelpad=10)
ax.set_ylabel("论文数量", fontsize=14, fontweight='bold', labelpad=10)

plt.tight_layout()
plt.savefig("literature_types_stacked_chart_with_labels.png", bbox_inches="tight", dpi=300, 
           facecolor='white', edgecolor='none')
plt.show()

logging.info("图表已保存为: literature_types_stacked_chart_with_labels.png")

# 11. ─── 输出统计信息 ──────────────────────────────────────
logging.info("\n年度文献类型统计完成！")
logging.info(f"原始论文数: {len(clean_data)} 篇")
logging.info(f"分割后记录数: {len(expanded_df)} 条（一篇论文可能对应多个类型）")
if len(years) > 0:
    logging.info(f"年份范围: {years.min()} - {years.max()}")
    logging.info(f"总文献类型数量: {len(type_counts)} 种")
    logging.info(f"图中显示的类型数量: {n_types} 种（全部显示）")

    # 输出每年的总计（注意：这是分割后的总计，会大于原始论文数）
    yearly_totals = simplified_crosstab.sum(axis=1)
    logging.info("\n各年度文献类型记录总数（分割后）:")
    for year, total in yearly_totals.items():
        logging.info(f"  {year}: {total} 条记录")
        
    # 输出所有文献类型的统计
    logging.info(f"\n所有文献类型统计:")
    for i, (lit_type, count) in enumerate(type_totals.items(), 1):
        logging.info(f"  {i}. {lit_type}: {count} 次")

else:
    logging.warning("⚠️ 没有有效的年份数据用于统计")

# 12. ─── 保存处理后的数据 ──────────────────────────────────
# 保存分割后的数据供进一步分析
expanded_df.to_csv("expanded_literature_data.csv", index=False, encoding='utf-8-sig')
simplified_crosstab.to_csv("literature_types_crosstab.csv", encoding='utf-8-sig')

logging.info("\n数据文件已保存:")
logging.info("- expanded_literature_data.csv: 分割后的详细数据")
logging.info("- literature_types_crosstab.csv: 年份-文献类型交叉表")
logging.info("- literature_types_stacked_chart_with_labels.png: 带标签的堆积图")