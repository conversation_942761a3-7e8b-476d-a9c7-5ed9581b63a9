import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import re
import sys
import io

# 设置输出编码
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

class InstitutionExtractor:
    def __init__(self):
        """
        初始化机构提取器
        """
        self.institution_counts = Counter()
        self.processed_papers = 0
        self.total_institutions = 0
        self.extraction_log = []
        
        # 机构关键词，按优先级排序
        self.institution_keywords = [
            'University', 'College', 'Hospital', 'Medical School', 'School of Medicine',
            'Centre', 'Center', 'Institute', 'Clinic', 'Medical Center', 'Health System',
            'Healthcare', 'Academy', 'Foundation', 'Society', 'Association'
        ]
        
        # 排除的部门关键词
        self.department_keywords = [
            'Department', 'Division', 'Unit', 'Section', 'Team', 'Service', 'Ward',
            'ICU', 'Intensive Care', 'Emergency', 'Surgery', 'Medicine', 'Nursing' ,'School of Medicine'
        ]
    
    def extract_institution_name(self, affiliation):
        """
        从完整的机构信息中提取机构名称
        
        Args:
            affiliation: 完整的机构信息字符串
            
        Returns:
            str: 提取的机构名称，如果未找到则返回None
        """
        if not affiliation or pd.isna(affiliation):
            return None
        
        affiliation = str(affiliation).strip()
        
        # 移除末尾的国家、邮编等信息
        # 通常格式是: Department, Institution, Address, Country
        parts = affiliation.split(',')
        
        institution_name = None
        
        # 查找包含机构关键词的部分
        for i, part in enumerate(parts):
            part = part.strip()
            
            # 检查是否包含机构关键词
            for keyword in self.institution_keywords:
                if keyword.lower() in part.lower():
                    # 找到机构关键词
                    
                    # 检查这个部分是否是部门名称
                    is_department = False
                    for dept_keyword in self.department_keywords:
                        if dept_keyword.lower() in part.lower() and keyword.lower() != part.lower():
                            # 如果包含部门关键词，且不是纯机构名称，则可能是部门
                            # 例如 "Intensive Care Unit" 包含 Unit，但不是机构
                            is_department = True
                            break
                    
                    if not is_department:
                        # 清理机构名称
                        institution_candidate = self._clean_institution_name(part)
                        if institution_candidate and len(institution_candidate) > 5:  # 过滤过短的名称
                            institution_name = institution_candidate
                            break
            
            if institution_name:
                break
        
        # 如果没有找到机构名称，尝试更宽松的匹配
        if not institution_name:
            # 从后往前查找，因为机构名称通常在部门之后
            for i in range(len(parts)-1, -1, -1):
                part = parts[i].strip()
                for keyword in self.institution_keywords:
                    if keyword.lower() in part.lower():
                        institution_candidate = self._clean_institution_name(part)
                        if institution_candidate and len(institution_candidate) > 5:
                            institution_name = institution_candidate
                            break
                if institution_name:
                    break
        
        return institution_name
    
    def _clean_institution_name(self, name):
        """
        清理机构名称
        """
        if not name:
            return None
        
        # 保留原始名称
        cleaned = name.strip()
        
        # 移除地址信息（通常包含数字）
        cleaned = re.sub(r',?\s*\d{4,6}.*$', '', cleaned)  # 移除邮编及之后的内容
        cleaned = re.sub(r',?\s*[A-Z]{2,3}\s*\d+.*$', '', cleaned)  # 移除州/省代码
        
        # 清理多余空格和标点
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        cleaned = re.sub(r'^[,.\s]+|[,.\s]+$', '', cleaned)
        
        return cleaned if cleaned else None
    
    def extract_institutions_from_affiliations(self, affiliations_text, separator='||'):
        """
        从机构信息中提取机构列表
        
        Args:
            affiliations_text: 机构信息文本
            separator: 分隔符，默认为'||'
            
        Returns:
            list: 提取到的机构名称列表（包含重复）
        """
        if pd.isna(affiliations_text) or not affiliations_text.strip():
            return []
        
        # 分割机构信息
        affiliations = str(affiliations_text).split(separator)
        institutions_in_paper = []
        
        for affiliation in affiliations:
            affiliation = affiliation.strip()
            if not affiliation:
                continue
            
            # 提取机构名称
            institution_name = self.extract_institution_name(affiliation)
            if institution_name:
                institutions_in_paper.append(institution_name)
        
        return institutions_in_paper
    
    def analyze_institutions_from_excel(self, excel_file_path, affiliation_column='F'):
        """
        从Excel文件分析机构发文量
        
        Args:
            excel_file_path: Excel文件路径  
            affiliation_column: 机构信息所在列名
            
        Returns:
            dict: 各机构发文量统计
        """
        print("开始加载数据...")
        
        # 读取Excel文件
        try:
            df = pd.read_excel(excel_file_path)
            print(f"成功加载数据，共{len(df)}行")
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return None
        
        # 检查目标列是否存在
        if affiliation_column not in df.columns:
            if len(df.columns) >= 6:
                affiliation_column = df.columns[5]  # 使用第6列（索引5）
                print(f"使用第6列: {affiliation_column}")
            else:
                print("错误: 找不到机构信息列")
                return None
        
        print(f"使用机构信息列: {affiliation_column}")
        
        # 重置计数器
        self.institution_counts = Counter()
        self.processed_papers = 0
        self.total_institutions = 0
        self.extraction_log = []
        
        print("\n开始处理论文数据...")
        
        for idx, row in df.iterrows():
            if idx % 100 == 0:  # 每处理100行显示一次进度
                print(f"处理进度: {idx+1}/{len(df)} 篇论文")
            
            affiliations_text = row[affiliation_column]
            
            # 提取该论文中的所有机构（包含重复）
            institutions_in_paper = self.extract_institutions_from_affiliations(affiliations_text)
            
            if institutions_in_paper:
                # 统计每个机构出现次数（不去重，每个出现都计算）
                for institution in institutions_in_paper:
                    self.institution_counts[institution] += 1
                
                self.total_institutions += len(institutions_in_paper)
                self.processed_papers += 1
                
                # 记录第一篇论文的详细信息作为示例
                if idx == 1:  # 第二行（索引1）
                    print(f"\n=== 第2行论文提取示例 ===")
                    print(f"原始机构信息: {affiliations_text}")
                    print(f"分割后的机构信息:")
                    affiliations = str(affiliations_text).split('||')
                    for i, aff in enumerate(affiliations, 1):
                        institution = self.extract_institution_name(aff.strip())
                        print(f"  {i}. {aff.strip()}")
                        print(f"     -> 提取机构: {institution}")
                    
                    print(f"\n提取的所有机构（包含重复）: {institutions_in_paper}")
                    
                    # 统计每个机构在这篇论文中的出现次数
                    paper_counter = Counter(institutions_in_paper)
                    print(f"该论文中各机构出现次数:")
                    for inst, count in paper_counter.items():
                        print(f"  - {inst}: {count}次")
                    
                    print(f"\n当前总体机构计数: {dict(self.institution_counts)}")
                    print("=" * 50)
        
        print(f"\n{'='*60}")
        print("处理完成!")
        print(f"成功处理论文数: {self.processed_papers}/{len(df)}")
        print(f"识别出的唯一机构数: {len(self.institution_counts)}")
        print(f"机构总出现次数: {self.total_institutions}")
        print(f"平均每篇论文机构数: {self.total_institutions / self.processed_papers:.2f}")
        
        return dict(self.institution_counts)
    
    def display_top_institutions(self, institution_counts, top_n=None):
        """
        显示发文量最高的机构
        
        Args:
            institution_counts: 机构发文量统计字典
            top_n: 显示前N个机构，如果为None则显示所有机构
        """
        if not institution_counts:
            print("没有数据可以显示")
            return
        
        sorted_institutions = sorted(institution_counts.items(), key=lambda x: x[1], reverse=True)
        
        if top_n is None:
            display_institutions = sorted_institutions
            print(f"\n所有机构发文量统计（共{len(sorted_institutions)}个机构）:")
        else:
            display_institutions = sorted_institutions[:top_n]
            print(f"\n发文量最高的前{top_n}个机构:")
        
        print("-" * 100)
        print(f"{'排名':<4} {'发文量':<8} {'机构名称'}")
        print("-" * 100)
        
        for rank, (institution, count) in enumerate(display_institutions, 1):
            print(f"{rank:<4} {count:<8} {institution}")
    
    def create_visualization(self, institution_counts, top_n=None):
        """
        创建机构发文量可视化图表
        
        Args:
            institution_counts: 机构发文量统计
            top_n: 显示前N个机构，如果为None则显示所有机构（建议不超过50个以保证可读性）
        """
        if not institution_counts:
            print("没有数据可以可视化")
            return
        
        # 获取所有机构或前N个机构
        sorted_institutions = sorted(institution_counts.items(), key=lambda x: x[1], reverse=True)
        
        if top_n is None:
            # 如果机构太多，建议只显示前30个以保证图表可读性
            if len(sorted_institutions) > 30:
                top_institutions = sorted_institutions[:30]
                print(f"机构数量过多({len(sorted_institutions)}个)，图表只显示前30个机构")
            else:
                top_institutions = sorted_institutions
                print(f"显示所有{len(sorted_institutions)}个机构")
        else:
            top_institutions = sorted_institutions[:top_n]
        
        institutions = [item[0] for item in top_institutions]
        counts = [item[1] for item in top_institutions]
        
        # 截断过长的机构名称
        display_names = []
        for name in institutions:
            if len(name) > 50:
                display_names.append(name[:47] + "...")
            else:
                display_names.append(name)
        
        # 设置字体（修复中文显示问题）
        plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']  # 使用英文字体
        plt.rcParams['axes.unicode_minus'] = False
        
        # 动态调整图表大小
        fig_height = max(8, len(display_names) * 0.5)
        plt.figure(figsize=(14, fig_height))
        bars = plt.barh(range(len(display_names)), counts, color='steelblue')
        
        # 设置标签和标题（使用英文）
        plt.yticks(range(len(display_names)), display_names)
        plt.xlabel('Number of Publications')
        plt.title(f'Institutions by Publication Count (Total: {len(sorted_institutions)} institutions)', 
                 fontsize=16, fontweight='bold')
        
        # 在条形图上添加数值标签
        for i, (bar, count) in enumerate(zip(bars, counts)):
            plt.text(count + max(counts) * 0.01, i, str(count), va='center', fontsize=10)
        
        # 调整布局
        plt.tight_layout()
        
        # 反转y轴，让发文量最高的机构显示在顶部
        plt.gca().invert_yaxis()
        
        # 添加网格线便于阅读
        plt.grid(axis='x', alpha=0.3)
        
        # 保存图片
        plt.savefig('institution_statistics.png', dpi=300, bbox_inches='tight')
        print(f"可视化图表已保存为: institution_statistics.png")
        
        plt.show()
    
    def save_results_to_excel(self, institution_counts, output_file='institution_statistics.xlsx'):
        """
        保存结果到Excel文件
        
        Args:
            institution_counts: 机构发文量统计
            output_file: 输出Excel文件名
        """
        if not institution_counts:
            print("没有数据可以保存")
            return
        
        # 创建DataFrame并按发文量降序排列
        df_institutions = pd.DataFrame(list(institution_counts.items()), 
                                     columns=['Institution', 'Publications'])
        df_institutions = df_institutions.sort_values('Publications', ascending=False)
        df_institutions.reset_index(drop=True, inplace=True)
        df_institutions.index += 1  # 排名从1开始
        
        # 保存到Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df_institutions.to_excel(writer, sheet_name='Institution_Statistics', index_label='Rank')
            
            # 添加统计信息工作表
            summary_data = {
                'Statistics': ['Total Papers', 'Total Institutions', 'Total Institution Occurrences', 'Average Institutions per Paper'],
                'Value': [self.processed_papers, len(institution_counts), 
                        self.total_institutions, 
                        round(self.total_institutions / self.processed_papers, 2) if self.processed_papers > 0 else 0]
            }
            df_summary = pd.DataFrame(summary_data)
            df_summary.to_excel(writer, sheet_name='Summary', index=False)
        
        print(f"统计结果已保存为: {output_file}")
        return df_institutions

def main():
    """
    主函数 - 测试机构提取功能
    """
    print("开始机构发文量统计分析...")
    
    # 创建分析器实例
    extractor = InstitutionExtractor()
    
    # 测试数据
    test_data = """Department of Anaesthesiology Nursing & Intensive Care, Medical University in Gdansk, 80211 Gdansk, Poland.||Intensive Care Unit, University Clinical Centre in Gdansk, 80211 Gdansk, Poland.||Independent Team of Physiotherapists, University Clinical Centre in Gdansk, 80211 Gdansk, Poland.||Department of Anaesthesiology Nursing & Intensive Care, Medical University in Gdansk, 80211 Gdansk, Poland.||Departament of Emergency, Institute of Health Sciences Medical College of Rzeszow University, 35310 Rzeszow, Poland.||Departament of Emergency, Institute of Health Sciences Medical College of Rzeszow University, 35310 Rzeszow, Poland."""
    
    print("=== 测试机构提取功能 ===")
    institutions = extractor.extract_institutions_from_affiliations(test_data)
    print(f"提取的机构（包含重复）: {institutions}")
    
    # 统计测试数据中各机构出现次数
    test_counter = Counter(institutions)
    print(f"\n测试数据中各机构出现次数:")
    for inst, count in sorted(test_counter.items(), key=lambda x: x[1], reverse=True):
        print(f"  - {inst}: {count}次")
    
    # 如果有Excel文件路径，可以取消注释下面的代码进行完整分析
    excel_file_path = r'C:\AI医疗\intensive care unit; nursing2025.8.6 原始数据及任务要求\2025.8.6导出数据.xlsx'  # 替换为实际路径
    institution_counts = extractor.analyze_institutions_from_excel(excel_file_path, 'F')
    
    if institution_counts:
        extractor.display_top_institutions(institution_counts, top_n=20)  # 显示前20个机构
        extractor.create_visualization(institution_counts, top_n=20)  # 可视化前20个机构
        extractor.save_results_to_excel(institution_counts)
        print(f"\n分析完成! 共识别出 {len(institution_counts)} 个机构")

if __name__ == "__main__":
    main()