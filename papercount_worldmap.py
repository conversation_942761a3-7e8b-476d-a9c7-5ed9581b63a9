import pandas as pd
import requests
import spacy
import io
import sys
import plotly.express as px
import plotly.graph_objects as go
from functools import lru_cache
from collections import Counter
import time
import re
from datetime import datetime

# 设置输出编码
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

class PreciseCountryExtractor:
    def __init__(self, geonames_username):
        """
        初始化精确国家提取器
        
        Args:
            geonames_username: GeoNames API用户名
        """
        self.username = "daizor"
        # 加载spaCy模型，只保留NER组件以提高性能
        try:
            self.nlp = spacy.load("en_core_web_sm", disable=["tagger", "parser", "attribute_ruler", "lemmatizer"])
        except IOError:
            print("错误: 请先安装spaCy英文模型:")
            print("python -m spacy download en_core_web_sm")
            sys.exit(1)
        
        # 缓存已查询的结果，避免重复API调用
        self.country_cache = {}
        
        # API调用计数器和限制
        self.api_calls = 0
        self.max_api_calls_per_hour = 1000  # GeoNames免费用户限制
        self.last_api_reset = datetime.now()
    
    @lru_cache(maxsize=1000)
    def get_standardized_country_name(self, location):
        """
        使用GeoNames API获取标准化的国家名称
        
        Args:
            location: 地理位置名称
            
        Returns:
            str: 标准化的国家名称，如果未找到则返回None
        """
        # 检查API调用限制
        current_time = datetime.now()
        if (current_time - self.last_api_reset).seconds > 3600:
            self.api_calls = 0
            self.last_api_reset = current_time
        
        if self.api_calls >= self.max_api_calls_per_hour:
            print(f"警告: 已达到API调用限制，跳过查询: {location}")
            return None
        
        # 检查缓存
        if location in self.country_cache:
            return self.country_cache[location]
        
        # 清理地理位置名称
        cleaned_location = self._clean_location_name(location)
        
        # 构建API请求参数
        params = {
            'q': cleaned_location,
            'username': self.username,
            'featureClass': 'A',  # 行政区划
            'maxRows': 5,  # 增加返回结果数量以提高匹配准确性
            'orderby': 'relevance',
            'type': 'json'
        }
        
        try:
            # 添加请求延迟以避免API限制
            time.sleep(0.1)
            response = requests.get("http://api.geonames.org/searchJSON", params=params, timeout=10)
            self.api_calls += 1
            
            if response.status_code == 200:
                data = response.json()
                
                # 检查API错误
                if 'status' in data:
                    print(f"GeoNames API错误: {data['status']['message']}")
                    return None
                
                geonames = data.get('geonames', [])
                print(f"查询位置: {location} -> 清理后: {cleaned_location}")
                print(f"GeoNames结果: {len(geonames)}个")
                
                # 寻找最佳匹配
                country_name = self._find_best_country_match(geonames, cleaned_location)
                
                # 缓存结果
                self.country_cache[location] = country_name
                return country_name
                
            else:
                print(f"API请求失败: HTTP {response.status_code}")
                return None
                
        except requests.RequestException as e:
            print(f"API请求异常: {e}")
            return None
    
    def _clean_location_name(self, location):
        """
        清理地理位置名称，移除干扰信息
        
        Args:
            location: 原始地理位置名称
            
        Returns:
            str: 清理后的地理位置名称
        """
        # 移除常见的机构词汇
        institution_words = [
            'university', 'hospital', 'center', 'centre', 'institute', 'department',
            'college', 'school', 'medical', 'health', 'clinic', 'research',
            'laboratory', 'faculty', 'division', 'unit'
        ]
        
        # 转换为小写进行处理
        cleaned = location.lower()
        
        # 移除邮编和数字
        cleaned = re.sub(r'\b\d{4,6}\b', '', cleaned)
        cleaned = re.sub(r'\b\d{1,5}\s+\w+\s+\w+\b', '', cleaned)  # 移除地址格式
        
        # 移除机构相关词汇
        for word in institution_words:
            cleaned = re.sub(r'\b' + word + r'\w*\b', '', cleaned)
        
        # 移除特殊字符和多余空格
        cleaned = re.sub(r'[^\w\s,.-]', '', cleaned)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # 提取最后几个地理位置相关的词（通常国家/州/城市在最后）
        words = [w.strip() for w in cleaned.split(',') if w.strip()]
        if words:
            # 返回最后1-3个词，这通常包含了地理位置信息
            return ', '.join(words[-3:]) if len(words) >= 3 else ', '.join(words)
        
        return cleaned
    
    def _find_best_country_match(self, geonames, query_location):
        """
        从GeoNames结果中找到最佳的国家匹配
        
        Args:
            geonames: GeoNames API返回的结果列表
            query_location: 查询的地理位置
            
        Returns:
            str: 最佳匹配的国家名称
        """
        # 优先级：国家 > 州/省 > 城市
        priority_fcodes = ['PCLI', 'ADM1', 'PPLA', 'PPL']
        
        for fcode in priority_fcodes:
            for geo in geonames:
                if geo.get('fcode') == fcode:
                    country_name = geo.get('countryName')
                    if country_name:
                        print(f"匹配成功: {query_location} -> {country_name} (特征码: {fcode})")
                        return country_name
        
        # 如果没有找到优先匹配，返回第一个有国家信息的结果
        for geo in geonames:
            country_name = geo.get('countryName')
            if country_name:
                print(f"匹配成功: {query_location} -> {country_name} (备选匹配)")
                return country_name
        
        print(f"未找到匹配: {query_location}")
        return None
    
    def extract_countries_from_affiliations(self, affiliations_text, separator='||'):
        """
        从机构信息中提取国家列表
        
        Args:
            affiliations_text: 机构信息文本
            separator: 分隔符，默认为'||'
            
        Returns:
            set: 提取到的国家集合
        """
        if pd.isna(affiliations_text) or not affiliations_text.strip():
            return set()
        
        # 分割机构信息
        affiliations = str(affiliations_text).split(separator)
        countries_in_paper = set()
        
        for affiliation in affiliations:
            affiliation = affiliation.strip()
            if not affiliation:
                continue
            
            print(f"\n处理机构: {affiliation}")
            
            # 使用spaCy进行命名实体识别
            doc = self.nlp(affiliation)
            
            # 提取地理政治实体(GPE)
            gpe_entities = [ent.text for ent in doc.ents if ent.label_ == 'GPE']
            
            if gpe_entities:
                # 使用最后一个GPE实体（通常是国家）
                last_gpe = gpe_entities[-1]
                print(f"识别的地理实体: {gpe_entities} -> 使用: {last_gpe}")
                
                # 使用GeoNames API获取标准化国家名称
                standard_country_name = self.get_standardized_country_name(last_gpe)
                if standard_country_name:
                    countries_in_paper.add(standard_country_name)
                    print(f"标准化国家名: {standard_country_name}")
            else:
                # 如果NLP没有识别出GPE，尝试直接查询整个机构信息
                print("未识别出地理实体，尝试查询整个机构信息")
                standard_country_name = self.get_standardized_country_name(affiliation)
                if standard_country_name:
                    countries_in_paper.add(standard_country_name)
                    print(f"从完整机构信息获取国家: {standard_country_name}")
        
        return countries_in_paper

def analyze_publications_by_country(excel_file_path, affiliation_column='F', geonames_username='daizor'):
    """
    分析论文按国家的分布
    
    Args:
        excel_file_path: Excel文件路径
        affiliation_column: 机构信息所在列名
        geonames_username: GeoNames用户名
        
    Returns:
        dict: 各国发文量统计
    """
    print("开始加载数据...")
    
    # 读取Excel文件
    try:
        df = pd.read_excel(excel_file_path)
        print(f"成功加载数据，共{len(df)}行")
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None
    
    # 检查目标列是否存在
    if affiliation_column not in df.columns:
        if len(df.columns) >= 6:
            affiliation_column = df.columns[5]  # 使用第6列（索引5）
            print(f"使用第6列: {affiliation_column}")
        else:
            print("错误: 找不到机构信息列")
            return None
    
    print(f"使用机构信息列: {affiliation_column}")
    
    # 初始化国家提取器
    extractor = PreciseCountryExtractor(geonames_username)
    
    # 统计各国发文量
    country_counts = Counter()
    processed_papers = 0
    unidentified_affiliations = []
    
    print("\n开始处理论文数据...")
    
    for idx, row in df.iterrows():
        print(f"\n{'='*50}")
        print(f"处理第 {idx+1}/{len(df)} 篇论文")
        
        affiliations_text = row[affiliation_column]
        
        # 提取该论文中的所有国家
        countries_in_paper = extractor.extract_countries_from_affiliations(affiliations_text)
        
        if countries_in_paper:
            # 每个国家在该论文中计数+1
            for country in countries_in_paper:
                country_counts[country] += 1
            print(f"论文 {idx+1} 涉及国家: {countries_in_paper}")
            processed_papers += 1
        else:
            print(f"论文 {idx+1} 未识别出国家")
            if affiliations_text:
                unidentified_affiliations.append(str(affiliations_text))
    
    print(f"\n{'='*60}")
    print("处理完成!")
    print(f"成功处理论文数: {processed_papers}/{len(df)}")
    print(f"识别出的国家数: {len(country_counts)}")
    print(f"未识别的论文数: {len(unidentified_affiliations)}")
    print(f"API调用次数: {extractor.api_calls}")
    
    # 显示统计结果
    print(f"\n各国发文量统计:")
    for country, count in country_counts.most_common():
        print(f"{country}: {count}")
    
    # 保存未识别的机构信息用于分析
    if unidentified_affiliations:
        print(f"\n未识别的机构信息示例（前10个）:")
        for i, affiliation in enumerate(unidentified_affiliations[:10]):
            print(f"{i+1}. {affiliation}")
    
    return dict(country_counts), unidentified_affiliations

def create_world_publication_map(country_counts, output_html='world_publication_map.html', output_image='world_publication_map.png'):
    """
    创建世界地图可视化（HTML和图片格式）
    
    Args:
        country_counts: 各国发文量统计字典
        output_html: 输出HTML文件名
        output_image: 输出图片文件名
    """
    if not country_counts:
        print("没有数据可以可视化")
        return None
    
    # 创建DataFrame
    df_map = pd.DataFrame(list(country_counts.items()), columns=['Country', 'Publications'])
    df_map = df_map.sort_values('Publications', ascending=False)
    
    print(f"\n准备可视化数据: {len(df_map)} 个国家")
    
    # 国家名称映射以确保plotly能正确识别
    country_name_corrections = {
        'United States of America': 'United States',
        'UK': 'United Kingdom',
        'South Korea': 'South Korea',
        'North Korea': 'North Korea'
    }
    
    df_map['Country_Display'] = df_map['Country'].replace(country_name_corrections)
    
    # 创建choropleth地图
    fig = px.choropleth(
        df_map,
        locations='Country_Display',
        color='Publications',
        locationmode='country names',
        color_continuous_scale='Viridis',
        title='全球论文发文量分布图',
        labels={'Publications': '发文量', 'Country_Display': '国家'},
        hover_name='Country_Display',
        hover_data={'Publications': True, 'Country_Display': False}
    )
    
    # 更新图表布局
    fig.update_layout(
        title={
            'text': '全球论文发文量分布图',
            'x': 0.5,
            'font': {'size': 20}
        },
        font_size=12,
        width=1400,
        height=800,
        geo=dict(
            showframe=False,
            showcoastlines=True,
            projection_type='equirectangular'
        )
    )
    
    # 保存HTML文件
    fig.write_html(output_html)
    print(f"交互式世界地图已保存为: {output_html}")
    
    # 保存图片文件
    try:
        # 检查是否安装了kaleido
        import kaleido
        
        # 保存为PNG图片
        fig.write_image(output_image, width=1400, height=800, scale=2)
        print(f"世界地图图片已保存为: {output_image}")
        
        # 也可以保存为其他格式
        if output_image.endswith('.png'):
            # 同时保存为PDF和SVG格式
            pdf_file = output_image.replace('.png', '.pdf')
            svg_file = output_image.replace('.png', '.svg')
            
            fig.write_image(pdf_file, width=1400, height=800)
            fig.write_image(svg_file, width=1400, height=800)
            
            print(f"同时保存了PDF格式: {pdf_file}")
            print(f"同时保存了SVG格式: {svg_file}")
            
    except ImportError:
        print("警告: 未安装kaleido库，无法保存图片格式")
        print("请运行以下命令安装: pip install kaleido")
        print("或者使用在线方式安装: pip install plotly[orca]")
    except Exception as e:
        print(f"保存图片时出错: {e}")
    
    # 显示图表
    fig.show()
    
    return fig

def save_results_to_excel(country_counts, unidentified_affiliations, output_file='country_statistics.xlsx'):
    """
    保存结果到Excel文件
    
    Args:
        country_counts: 各国发文量统计
        unidentified_affiliations: 未识别的机构列表
        output_file: 输出Excel文件名
    """
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 保存国家统计
        df_countries = pd.DataFrame(list(country_counts.items()), columns=['Country', 'Publications'])
        df_countries = df_countries.sort_values('Publications', ascending=False)
        df_countries.to_excel(writer, sheet_name='Country_Statistics', index=False)
        
        # 保存未识别的机构
        if unidentified_affiliations:
            df_unidentified = pd.DataFrame(unidentified_affiliations, columns=['Unidentified_Affiliations'])
            df_unidentified.to_excel(writer, sheet_name='Unidentified', index=False)
    
    print(f"统计结果已保存为: {output_file}")

def main():
    """
    主函数
    """
    # 配置参数
    excel_file_path = r'C:\AI医疗\intensive care unit; nursing2025.8.6 原始数据及任务要求\2025.8.6导出数据.xlsx'  # 请修改为你的文件路径
    affiliation_column = 'F'  # 机构信息所在列
    geonames_username = 'daizor'  # 请使用你自己的GeoNames用户名
    
    print("开始精确国家提取和分析...")
    print(f"数据文件: {excel_file_path}")
    print(f"GeoNames用户名: {geonames_username}")
    
    # 分析数据
    try:
        country_counts, unidentified = analyze_publications_by_country(
            excel_file_path, 
            affiliation_column, 
            geonames_username
        )
        
        if country_counts:
            # 创建世界地图（HTML + 图片格式）
            create_world_publication_map(
                country_counts,
                output_html='world_publication_map.html',
                output_image='world_publication_map.png'
            )
            
            # 保存结果
            save_results_to_excel(country_counts, unidentified)
            
            print(f"\n分析完成! 共识别出 {len(country_counts)} 个国家的发文量")
        else:
            print("分析失败，没有获得有效数据")
            
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()