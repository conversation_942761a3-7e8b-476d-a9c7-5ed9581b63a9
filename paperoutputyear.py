import logging
import re
from pathlib import Path

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# 0. ─── 日志设置 ──────────────────────────────────────────
logging.basicConfig(
    level=logging.INFO,         
    format='[%(levelname)s] %(message)s'
)
# 1. ─── 路径与列索引 ───────────────────────────────────────
xls_path = Path(
    r"C:\AI医疗\intensive care unit; nursing2025.8.6 原始数据及任务要求\2025.8.6导出数据.xlsx"
)
col_idx = 27                    # AB 列（0 起）

# 2. ─── 读取列 ────────────────────────────────────────────
raw = pd.read_excel(xls_path, usecols=[col_idx])
raw.columns = ["raw_date"]
expected_rows = 4268
actual_rows   = len(raw)

logging.info(f"读取列完成，共 {actual_rows} 行；目标应有 {expected_rows} 行")
if actual_rows != expected_rows:
    logging.warning("⚠️ 行数与期望不符，请确认 Excel 是否改动！")

# 3. ─── 年份抽取函数（正则优先）──────────────────────────
def extract_year(text: str | float) -> int | None:
    """
    先用正则抓 4 位数字；抓不到再走 to_datetime；
    仍解析失败则返回 None
    """
    if pd.isna(text):
        return None

    # step 1: 正则
    m = re.search(r"\b(\d{4})\b", str(text))
    if m:
        yr = int(m.group(1))
        if 1800 <= yr <= 2100:     # 简单年份范围校验
            return yr

    # step 2: datetime 兜底
    try:
        return pd.to_datetime(text, errors="raise").year
    except Exception:
        return None

# 向量化处理
raw["Year"] = raw["raw_date"].apply(extract_year)

# 4. ─── 校验解析结果 ───────────────────────────────────────
failed = raw[raw["Year"].isna()]
if not failed.empty:
    logging.warning(f"❌ 仍有 {len(failed)} 行无法解析年份！")
    logging.warning("无法解析示例：")
    logging.warning(failed.head(10).to_string(index=False))
else:
    logging.info("✅ 全部行成功解析年份")

assert failed.empty, "仍有无法解析的日期，请检查数据格式！"

# 5. ─── 统计年度发文量 ────────────────────────────────────
year_cnt = raw["Year"].value_counts().sort_index()
logging.info("各年份发文量：")
for yr, cnt in year_cnt.items():
    logging.info(f"  {yr}: {cnt}")

# 6. ─── 作图 ─────────────────────────────────────────────
sns.set_theme(style="whitegrid")
plt.rcParams["font.family"]        = ["SimHei"]
plt.rcParams["axes.unicode_minus"] = False

fig, ax = plt.subplots(figsize=(8, 5), dpi=300)
x, y = year_cnt.index, year_cnt.values

ax.plot(x, y,
        color="#1f77b4",
        marker="o",
        linewidth=2.2,
        markersize=4,
        label="年发文量")
ax.fill_between(x, y, color="#1f77b4", alpha=0.15)

for xi, yi in zip(x, y):
    ax.text(xi, yi + max(y)*0.02, f"{yi}", ha="center", va="bottom", fontsize=8)

ax.set_xlabel("年份", fontsize=12)
ax.set_ylabel("论文数量", fontsize=12)
ax.set_title("图1  年度论文产出量", fontsize=14, weight="bold")
ax.set_xticks(x); plt.setp(ax.get_xticklabels(), rotation=45, ha="right")
sns.despine(ax=ax)
ax.legend(frameon=False, loc="upper left")
plt.tight_layout()
plt.savefig("annual_publication_trend.png", bbox_inches="tight")
plt.show()  